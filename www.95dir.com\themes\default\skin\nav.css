@charset"utf-8";@import url("reset.css");body{background:#fff url(bg.png) repeat-x top}body,td,th,input,select,textarea{color:#555;font:12px/1.5 "微软雅黑"}a{color:#555;text-decoration:none}a:hover{color:#f30;text-decoration:underline}.blank10{clear:both;display:block;height:10px;width:100%}#topbg{height:32px;line-height:32px}#topbar{margin:0 auto;width:1200px}#topbar-left{float:left;font-size:12px}#topbar-right{color:#ccc;float:right;font-size:12px}#topbar-right img{vertical-align:middle}#wrapper{margin:0 auto;width:1200px}#topbox{height:100px;position:relative}.logo{background:url(logo.png) center;background-size:contain;display:block;float:left;height:100px;width:220px;position:relative;overflow:hidden}.logo::before{content:'';position:absolute;top:0;left:-50%;width:10%;height:100%;background:linear-gradient(to right,transparent 0%,rgba(255,255,255,.1)30%,rgba(255,255,255,.6)50%,rgba(255,255,255,.1)70%,transparent 100%);filter:blur(8px);animation:sweep 3s ease-in-out infinite;transform:rotate(15deg);transform-origin:center}@keyframes sweep{0%{left:-50%}50%{left:50%}100%{left:100%}}#sobox{float:right;padding-top:20px}.sofrm{display:block;margin:0 auto;padding-top:12px;position:relative}.sipt{background:url(ipt.png) no-repeat top left;border:1px solid #dadada;display:block;font:normal 13px/30px normal;float:left;height:30px;padding:0 5px 0 90px;width:300px}.sbtn{background:#65bc0b;border:0;color:#fff;cursor:pointer;font-size:12px;height:32px;width:70px}#selopt{background:url(select.gif) no-repeat;position:absolute;left:2px;top:17px;width:88px}#cursel{cursor:pointer;display:block;height:28px;line-height:28px;overflow:hidden;text-indent:10px;width:85px;font-size:13px}#options{border:1px solid #dadada;border-top:0;display:none;list-style:none;position:absolute;left:-2px;width:80px;z-index:1000;background:#fff}#options li{background:#fff;clear:both;cursor:pointer}#options li a{color:#555;display:block;height:25px;line-height:25px;text-decoration:none;text-align:center;font-size:13px}#options li a:hover{background:#1791de;color:#fff}.current{background:#1791de;color:#fff;display:block;text-decoration:none}#navbox{background:url(blue.png) repeat-x;display:block;height:35px}.navbar li{float:left;font:14px/35px "微软雅黑";height:35px;text-align:center;width:100px}.navbar li a{display:block;color:#fff}.navbar li a:hover{background:#0080c6;color:#fff}.navbar .navline{background:#0797e5;display:block;height:35px;width:1px}.navbar .cur{background:#0067ae}#txtbox{background:url(blue.png) repeat-x 0 -55px;border-left:1px solid #dae7ed;border-right:1px solid #dae7ed;height:40px}.count{float:left;padding:10px;font-size:13px}.count b{color:#f60;font:bold 16px Arial;padding-right:3px}.link{color:#999;float:right;padding:10px}.link a{color:#06c}#quickbox{background:#f9fef4;border:1px dashed #cbe6bd;overflow:hidden;padding:6px;white-space:nowrap}#quickbox a{margin-right:15px}#homebox-left{float:left;width:310px}#homebox-right{float:right;width:880px}#hcatebox{background:#f8fdff;border:1px solid #dae7ed;padding:8px}#hcatebox dt{clear:both;display:block;font:bold 14px/25px "微软雅黑";height:25px}#hcatebox dt a{color:#07c}.hcatelist{list-style:none;padding:0;margin:0;display:flex;flex-wrap:wrap;padding-top:6px}.hcatelist li{margin-right:3px;margin-bottom:5px}.hcatelist li a{display:block;padding:2px 4px;background:#fff;border:1px solid #ddd;border-radius:5px;font-size:13px;color:#333}.hcatelist li a:hover{background:#e9ecef;text-decoration:none}#newbox{border:1px solid #dae7ed}#newbox h3{background:#f8fdff;border-bottom:1px dashed #dae7ed;color:#07c;font-size:14px;padding:6px}.newlist{padding:3px 8px}.newlist li{padding:5px 0;white-space:nowrap}.newlist li img{top:4px;position:relative;margin-right:4px}.newlist li a{display:block;overflow:hidden;font-size:13px;}.newlist li span{color:#aea8a8;float:right;margin-top:6px}#bestbox{border:1px solid #dae7ed}#bestbox h3{background:#f8fdff;border-bottom:1px solid #dae7ed;font:bold 14px normal;height:30px}#bestbox h3 span{background:#fff;border:1px solid #cedee6;border-bottom:0;color:#07c;display:block;float:left;height:25px;line-height:25px;margin-left:5px;margin-top:5px;text-align:center;width:80px}.bestlist{padding:8px}.bestlist li{display:block;float:left;height:30px;line-height:30px;margin-right:12px;overflow:hidden;white-space:nowrap;width:105px;box-shadow:0 2px 8px rgba(0,0,0,.06)}.bestlist li img{top:4px;position:relative;margin-right:6px}.bestlist li a{font-size:13px;color:#f31818}#coolbox{border:1px solid #dae7ed}#coolbox h3{background:#f8fdff;border-bottom:1px dashed #dae7ed;color:#07c;font-size:14px;padding:6px}.csitelist{padding:5px 8px}.csitelist li{display:block;font-size:14px;height:30px;overflow:hidden;vertical-align:top;width:100%}.csitelist li h4{display:block;float:left;font-weight:400;height:30px;line-height:30px;width:70px}.csitelist li h4 a{color:#07c}.csitelist li span{display:block;float:left;height:30px;line-height:30px;margin-right:10px;overflow:hidden;white-space:nowrap;width:82px}.csitelist li span img{top:4px;position:relative;margin-right:6px}.csitelist li span a{font-size:13px}.csitelist .more{color:#07c;float:right;font-size:12px;line-height:30px}.sline{background:url(dot.gif) repeat-x center;display:block;height:10px}#rowbox{border:1px solid #dae7ed;padding:10px;height:422px}#newsbox{float:left;width:50%}#newsbox h3{color:#07c;font-size:14px;padding-bottom:6px}.newslist li{padding:6px 0;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.newslist li span{color:#aea8a8;float:right;font-size:11px;margin-top:2px}#exlink{float:right;width:410px}#exlink h3{color:#07c;font-size:14px;padding-bottom:6px}.exlist li{padding:6px 0;white-space:nowrap;overflow:hidden}.exlist li span{color:#aea8a8;float:right;font-size:11px}.line{border-left:1px dashed #dadada;float:left;height:342px;margin-left:10px;width:1px}#inbox,#linkbox{background:#f8fdff;border:1px solid #dae7ed;border-radius:5px;padding:10px}#inbox h3,#linkbox h3{font-size:14px;margin-bottom:10px;color:#07c}.inlist,.linklist{list-style:none;padding:0;margin:0;display:flex;flex-wrap:wrap}.inlist li,.linklist li{margin-right:6px;margin-bottom:10px}.inlist li img{top:4px;position:relative;margin-right:6px}.inlist li a,.linklist li a{display:block;background:#fff;border:1px solid #ddd;border-radius:5px;font-size:13px;color:#333;width:99.5px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.inlist li a:hover,.linklist li a:hover{background:#e9ecef;text-decoration:none}#inbox1{border:1px solid #dae7ed}#inbox1 h3{background:#f8fdff;border-bottom:1px dashed #dae7ed;color:#07c;font-size:14px;padding:6px}.inlist1 li{float:left;white-space:nowrap;overflow:hidden;text-align:center;width:128px;border:1px solid #eee;padding:5px;margin:5px;}.inlist li{float:left;height:23px;line-height:23px;white-space:nowrap}#linkbox{background:#f8fdff;border:1px solid #dae7ed;padding:5px 8px}#linkbox h3{float:left;height:23px;line-height:23px;width:60px}.linklist li{float:left;height:23px;line-height:23px;margin-right:20px;vertical-align:top;white-space:nowrap}#footer{background:url(fbg.png) repeat-x;padding:10px;text-align:center}#fmenu{color:#ccc;padding-bottom:5px}#fmenu a{text-decoration:none}#fmenu a:hover{color:#f60;text-decoration:underline}#fcopy{line-height:23px}.newslist{list-style:none;padding-left:0;counter-reset:rank}.newslist li{position:relative;padding-left:26px;counter-increment:rank}.newslist li::before{content:counter(rank);position:absolute;left:5px;top:50%;transform:translateY(-50%);font:700 18px/1 'Roboto Condensed',Arial,sans-serif;color:#666}.newslist li:nth-child(1)::before{color:#ff4757;font-size:22px}.newslist li:nth-child(2)::before{color:#ffa502;font-size:20px}.newslist li:nth-child(3)::before{color:#2ed573;font-size:18px}.newslist li:nth-child(n+4)::before{color:#a4b0be;font-weight:500}.site-notice ul{padding:0;margin:0}.site-notice{overflow:hidden;line-height:30px;height:22px;width:670px;float:left}.site-notice li{padding-left:10px;height:30px}#newsbox span1 a{float:right;margin-top:-24px;color:#07c}.arcbox-list{font-size:13px}.arcbox-list li{padding:2px 0}.arcbox-list li strong,.arcbox-list li b{padding:0 3px;background:#e74c3c;color:#fff;margin-right:13px}.arcbox-list li a{padding:0 8px;color:#555}.newbox .arcbox-list li a{padding:0 2px!important}.uzkoo{display: none !important;}.vip-title{display:inline-block;font-family:'Arial Black',sans-serif;font-size:18px;font-weight:bold;background:linear-gradient(45deg,#FFD700,#C0C0C0,#FFD700);-webkit-background-clip:text;background-clip:text;color:transparent;text-shadow:0 0 10px rgba(255,215,0,0.5),0 0 20px rgba(255,215,0,0.3);animation:glow 2s ease-in-out infinite alternate;letter-spacing:2px;position:relative}@keyframes glow{from{text-shadow:0 0 5px rgba(255,215,0,0.3),0 0 10px rgba(255,215,0,0.2)}to{text-shadow:0 0 10px rgba(255,215,0,0.5),0 0 20px rgba(255,215,0,0.4),0 0 30px rgba(255,215,0,0.2)}}

/* 渐变动画定义 */
@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}.donate-button{position:relative;overflow:hidden;margin-top:-4px}.donate-button:hover{background-color:#e55a00}.shine-effect{position:absolute;top:0;left:-100%;width:50%;height:100%;background:linear-gradient(to right,transparent,rgba(255,255,255,0.3),transparent);transform:skewX(-25deg);animation:shine 2s infinite}@keyframes shine{0%{left:-100%}100%{left:100%}}#donate-popup{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;display:flex;justify-content:center;align-items:center}.donate-popup-content{background:#fff;padding:20px;border-radius:8px;width:80%;max-width:600px;text-align:center;position:relative}.donate-popup-content .close{position:absolute;top:10px;right:10px;font-size:24px;cursor:pointer;color:#666}.donate-popup-content h3{margin:0 0 20px 0;color:#333;font-size:18px}.donate-qr-codes{display:flex;justify-content:space-around;flex-wrap:wrap}.donate-qr-codes div{margin:10px;text-align:center}.donate-qr-codes h4{margin:10px 0;color:#555;font-size:16px}.donate-qr-codes img{width:150px;height:200px;border:1px solid #ddd;border-radius:4px}#bestbox ul li {margin: 3px;border-radius: 5px;}.inlist1 li{display:inline-block;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,0,0,.15);transition:transform .25s,box-shadow .25s;position:relative}.inlist1 li:hover{transform:translateY(-6px) scale(1.03);box-shadow:0 8px 20px rgba(0,0,0,.25);z-index:2}.inlist1 li img.thumb{border-radius:4px}/* ==================== 热门标签样式 ==================== */
.hot-tags-container {
    padding: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: flex-start;
    justify-content: flex-start;
    line-height: 1.4;
}

.hot-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    color: white;
    text-decoration: none;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 150px;
    min-width: 40px;
    flex-shrink: 0;
    word-break: keep-all;
}

/* 30种不同颜色的标签 */
.hot-tag:nth-child(1) { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
.hot-tag:nth-child(2) { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.hot-tag:nth-child(3) { background: linear-gradient(135deg, #45b7d1, #3498db); }
.hot-tag:nth-child(4) { background: linear-gradient(135deg, #96ceb4, #85c1a3); }
.hot-tag:nth-child(5) { background: linear-gradient(135deg, #feca57, #ff9ff3); }
.hot-tag:nth-child(6) { background: linear-gradient(135deg, #ff9ff3, #f368e0); }
.hot-tag:nth-child(7) { background: linear-gradient(135deg, #54a0ff, #2e86de); }
.hot-tag:nth-child(8) { background: linear-gradient(135deg, #5f27cd, #341f97); }
.hot-tag:nth-child(9) { background: linear-gradient(135deg, #00d2d3, #01a3a4); }
.hot-tag:nth-child(10) { background: linear-gradient(135deg, #ff6348, #e17055); }
.hot-tag:nth-child(11) { background: linear-gradient(135deg, #2ed573, #1e90ff); }
.hot-tag:nth-child(12) { background: linear-gradient(135deg, #ffa502, #ff6348); }
.hot-tag:nth-child(13) { background: linear-gradient(135deg, #3742fa, #2f3542); }
.hot-tag:nth-child(14) { background: linear-gradient(135deg, #70a1ff, #5352ed); }
.hot-tag:nth-child(15) { background: linear-gradient(135deg, #7bed9f, #2ed573); }
.hot-tag:nth-child(16) { background: linear-gradient(135deg, #ff4757, #c44569); }
.hot-tag:nth-child(17) { background: linear-gradient(135deg, #2f3542, #40407a); }
.hot-tag:nth-child(18) { background: linear-gradient(135deg, #ff3838, #ff4757); }
.hot-tag:nth-child(19) { background: linear-gradient(135deg, #0984e3, #74b9ff); }
.hot-tag:nth-child(20) { background: linear-gradient(135deg, #6c5ce7, #a29bfe); }
.hot-tag:nth-child(21) { background: linear-gradient(135deg, #fd79a8, #fdcb6e); }
.hot-tag:nth-child(22) { background: linear-gradient(135deg, #e84393, #fd79a8); }
.hot-tag:nth-child(23) { background: linear-gradient(135deg, #00b894, #00cec9); }
.hot-tag:nth-child(24) { background: linear-gradient(135deg, #e17055, #d63031); }
.hot-tag:nth-child(25) { background: linear-gradient(135deg, #81ecec, #74b9ff); }
.hot-tag:nth-child(26) { background: linear-gradient(135deg, #fab1a0, #e17055); }
.hot-tag:nth-child(27) { background: linear-gradient(135deg, #00b894, #55a3ff); }
.hot-tag:nth-child(28) { background: linear-gradient(135deg, #fd79a8, #e84393); }
.hot-tag:nth-child(29) { background: linear-gradient(135deg, #fdcb6e, #e17055); }
.hot-tag:nth-child(30) { background: linear-gradient(135deg, #6c5ce7, #fd79a8); }

/* 超过30个标签的循环颜色 */
.hot-tag:nth-child(n+31) { background: linear-gradient(135deg, #a29bfe, #74b9ff); }

.hot-tag:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    text-decoration: none;
    color: white;
    filter: brightness(1.1);
}

.hot-tag .tag-count {
    background: rgba(255,255,255,0.9);
    border-radius: 10px;
    padding: 1px 5px;
    margin-left: 5px;
    font-size: 10px;
    font-weight: bold;
    color: #333;
    text-shadow: none;
}

.hot-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.hot-tag:hover::before {
    left: 100%;
}

.icon-orange {
    color: #ff8c00;
}

/* 热门标签加载状态 */
.hot-tags-container.loading {
    position: relative;
    min-height: 60px;
}

.hot-tags-container.loading::before {
    content: '加载中...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 12px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 热门标签错误状态 */
.hot-tags-container.error {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 12px;
}

.hot-tags-container.error::before {
    content: '标签加载失败，请刷新页面重试';
}

/* ==================== 热门排行榜样式 ==================== */
#toprank-box {
    border: 1px solid #e8e8e8;
    margin-bottom: 15px;
}

#toprank-box h3 {
    background: url(blue.png) 0 -95px repeat-x;
    font-size: 14px;
    padding: 6px;
    margin: 0;
    position: relative;
}

.toprank-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 2px;
    margin: 0;
    padding: 8px;
    list-style: none;
}

.toprank-item {
    position: relative;
}

.toprank-link {
    display: block;
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.toprank-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #ff6b6b;
}

.toprank-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    font-size: 9px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 10px;
    color: #fff;
    text-transform: uppercase;
    z-index: 2;
    min-width: 18px;
    text-align: center;
}

/* 前三名特殊样式 */
.toprank-badge.rank-1 {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
}

.toprank-badge.rank-2 {
    background: linear-gradient(135deg, #ffa726, #ff9800);
    box-shadow: 0 2px 8px rgba(255, 167, 38, 0.4);
}

.toprank-badge.rank-3 {
    background: linear-gradient(135deg, #66bb6a, #4caf50);
    box-shadow: 0 2px 8px rgba(102, 187, 106, 0.4);
}

/* 其他排名样式 */
.toprank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: linear-gradient(135deg, #78909c, #607d8b);
    box-shadow: 0 2px 8px rgba(120, 144, 156, 0.4);
}

.toprank-stats {
    font-size: 10px;
    color: #999;
    margin-top: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
}

.rank-views {
    display: flex;
    align-items: center;
    gap: 2px;
}

.rank-views i {
    font-size: 9px;
}

/* 响应式适配 */
@media (max-width: 767px) {
    .toprank-list {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 6px;
    }

    .toprank-link {
        padding: 8px 6px;
        min-height: 50px;
    }

    .recommend-name {
        font-size: 10px;
    }

    .toprank-stats {
        font-size: 9px;
    }
}

/* 桌面端默认样式 - 数据归档月份链接 */
.archive-month-link {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 4px;
    text-decoration: none;
    color: #666;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 13px; /* 增大字体 */
    font-weight: 500;
}

.archive-month-link:hover {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 300%;
    animation: gradientAnimation 2s ease infinite;
    color: white;
    text-decoration: none;
    border-color: transparent;
    transform: scale(1.05);
}

.archive-month-link.active {
    background: linear-gradient(45deg, #007cba, #0056b3, #004085);
    background-size: 200% 200%;
    animation: gradientAnimation 3s ease infinite;
    color: white;
    border-color: #007cba;
    font-weight: 600;
}

.archive-month-link.scrolling {
    background: linear-gradient(45deg, #e74c3c, #c0392b, #a93226, #922b21);
    background-size: 300% 300%;
    animation: gradientAnimation 1.5s ease infinite;
    color: white;
    border-color: #e74c3c;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

/* 桌面端默认样式 - 文章分类链接 */
.article-cate-link {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px 3px 2px 0;
    font-size: 13px; /* 增大字体 */
    color: #666;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #ddd;
    font-weight: 500;
}

.article-cate-link:hover {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 300%;
    animation: gradientAnimation 2s ease infinite;
    color: white;
    text-decoration: none;
    border-color: transparent;
    transform: scale(1.05);
}

.article-cate-link.active {
    background: linear-gradient(45deg, #e74c3c, #c0392b, #a93226);
    background-size: 200% 200%;
    animation: gradientAnimation 3s ease infinite;
    color: white;
    border-color: #e74c3c;
    font-weight: 600;
}

.article-cate-link.scrolling {
    background: linear-gradient(45deg, #f39c12, #e67e22, #d35400, #c0392b);
    background-size: 300% 300%;
    animation: gradientAnimation 1.5s ease infinite;
    color: white;
    border-color: #f39c12;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
}

@media(max-width:768px){body{background:#fff}#wrapper{margin:0 auto;width:100%}#navbox{background:#0089d4;display:block;height:106px}.navbar li{float:left;font:14px/35px "微软雅黑";height:35px;text-align:center;width:25%}li.navline,.navbar li:nth-last-child(2){display:none!important}.logo{background:url(logo.png) center;background-size:100% 100%;display:block;float:left;height:60px;width:140px}#topbox{height:60px}#sobox{float:right;padding-top:0}.sipt{background:url(ipt.png) no-repeat top left;border:1px solid #dadada;display:block;font:normal 13px/30px normal;float:left;height:30px;padding:0 5px 0 90px;width:80px}.sofrm{display:block;margin:0 auto;padding-top:14px;position:relative;width:auto}.sbtn{background:#65bc0b;border:0;color:#fff;cursor:pointer;font-size:14px;height:32px;width:50px;margin-right:3px}#homebox,#inbox,#linkbox,#inbox1{margin:1%}#inbox{margin-top:0}#quickbox{background:#ffc;border:1px dashed #f60;overflow:hidden;width:96%;height:50px;margin:0 auto;line-height:25px;padding:1%}#homebox-left{float:left;width:100%;margin-top:-10px}.slxx{float:left;width:99%;height:96px;margin:0;border:1px solid #DBDBDB}.slxx dl{float:left;width:100%;position:relative;padding-top:10px;height:50px}#homebox-right{float:right;width:100%}#newsbox,#exlink{float:left;width:100%}.inlist1 li img{width:100%;height:50px}.bestlist li{display:block;float:left;height:30px;line-height:30px;margin-right:1.3%;overflow:hidden;padding-left:0;white-space:nowrap;width:28.3%}.site-notice{overflow:hidden;line-height:16px;height:40px;width:100%;float:left}.site-notice li{padding-left:5px;padding-right:5px;height:40px}#topbar-left,#topbg,.line{display:none}.inlist li a,.linklist li a{width:81px}#exlink h3{color:#07c;font-size:14px;padding-bottom:6px;margin-top:8px}#bestbox{border:1px solid #dae7ed;margin-top:10px}.inlist1 li{float: left;margin-bottom: 10px;margin-top: 10px;margin-right: 10px;white-space: nowrap;overflow: hidden;text-align: center;width: 115px;border: 1px solid #eee;padding: 0px;}.newlist li a{display: block;overflow: hidden;font-size: 13px;width: 260px;}

/* 移动端样式覆盖 */
.archive-month-link {
    padding: 2px 5px;
    margin: 1px;
    font-size: 10px;
}

.article-cate-link {
    padding: 2px 5px;
    margin: 1px 2px 1px 0;
    font-size: 10px;
}

/* 选项卡移动端优化 */
.tab-item {
    padding: 10px 4px;
    font-size: 12px;
}

.tab-item span {
    font-size: 12px !important;
}

.tab-item i {
    margin-right: 3px !important;
}}
@media(max-width:480px){body{background:#fff}#wrapper{margin:0 auto;width:100%}#navbox{background:#0089d4;display:block;height:106px}.navbar li{float:left;font:14px/35px "微软雅黑";height:35px;text-align:center;width:25%}li.navline,.navbar li:nth-last-child(2){display:none!important}.logo{background:url(logo.png) center;background-size:100% 100%;display:block;float:left;height:60px;width:140px}#topbox{height:60px}#sobox{float:right;padding-top:0}.sipt{background:url(ipt.png) no-repeat top left;border:1px solid #dadada;display:block;font:normal 13px/30px normal;float:left;height:30px;padding:0 5px 0 90px;width:80px}.sofrm{display:block;margin:0 auto;padding-top:14px;position:relative;width:auto}.sbtn{background:#65bc0b;border:0;color:#fff;cursor:pointer;font-size:14px;height:32px;width:50px;margin-right:3px}#homebox,#inbox,#linkbox,#inbox1{margin:1%}#inbox{margin-top:0}#quickbox{background:#ffc;border:1px dashed #f60;overflow:hidden;width:96%;height:50px;margin:0 auto;line-height:25px;padding:1%}#homebox-left{float:left;width:100%;margin-top:-10px}.slxx{float:left;width:99%;height:96px;margin:0;border:1px solid #DBDBDB}.slxx dl{float:left;width:100%;position:relative;padding-top:10px;height:50px}#homebox-right{float:right;width:100%}#newsbox,#exlink{float:left;width:100%}.inlist1 li img{width:100%;height:50px}.bestlist li{display:block;float:left;height:30px;line-height:30px;margin-right:1.3%;overflow:hidden;padding-left:0;white-space:nowrap;width:28.3%}.site-notice{overflow:hidden;line-height:16px;height:40px;width:100%;float:left}.site-notice li{padding-left:5px;padding-right:5px;height:40px}#topbar-left,#topbg,.line{display:none}.inlist li a,.linklist li a{width:78px}#exlink h3{color:#07c;font-size:14px;padding-bottom:6px;margin-top:8px}#bestbox{border:1px solid #dae7ed;margin-top:10px}.inlist1 li{float: left;margin-bottom: 10px;margin-top: 10px;margin-right: 10px;white-space: nowrap;overflow: hidden;text-align: center;width: 108px;border: 1px solid #eee;padding: 0px;}.newlist li a{display: block;overflow: hidden;font-size: 13px;width: 260px;}.csitelist li span{width: 74px;}.count{display:none}.uzkoo{display: none !important;}

/* 480px以下移动端样式覆盖 */
.archive-month-link {
    padding: 2px 5px;
    margin: 1px;
    font-size: 10px;
}

.article-cate-link {
    padding: 2px 5px;
    margin: 1px 2px 1px 0;
    font-size: 10px;
}

/* 选项卡超小屏幕优化 */
.tab-item {
    padding: 8px 2px;
    font-size: 11px;
}

.tab-item span {
    font-size: 11px !important;
}

.tab-item i {
    display: none;
}

/* 推荐列表超小屏幕优化 */
.bestlist-enhanced {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 5px;
}

/* 省份标签超小屏幕优化 */
.province-tags-list .province-tag {
    font-size: 11px;
    padding: 3px 6px;
}

/* 统计样式超小屏幕优化 */
.stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.spider-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
}

.stat-card, .spider-card {
    min-height: 60px;
}
}

/* ==================== 省份标签样式 ==================== */
.province-tags-list .province-tag {
    display: inline-block;
    padding: 5px 10px;
    background: #eaeef3;
    border-radius: 3px;
    color: #555;
    font-size: 13px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.province-tags-list .province-tag:hover {
    background: #1791de;
    color: #fff;
}

/* 不同类型省份的特殊样式 */
.province-tags-list .province-tag.municipality {
    background: #ffe6e6;
    color: #dc3545;
}

.province-tags-list .province-tag.municipality:hover {
    background: #dc3545;
    color: #fff;
}

.province-tags-list .province-tag.autonomous {
    background: #e6f7e6;
    color: #28a745;
}

.province-tags-list .province-tag.autonomous:hover {
    background: #28a745;
    color: #fff;
}

.province-tags-list .province-tag.special {
    background: #fff3cd;
    color: #856404;
}

.province-tags-list .province-tag.special:hover {
    background: #ffc107;
    color: #212529;
}

/* 点击动画效果 */
.province-tag.clicked {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* ==================== 统计容器样式 ==================== */
#todayStatsBox, #spiderStatsBox {
    border: 1px solid #dae7ed;
    padding: 8px;
}

#todayStatsBox h3, #spiderStatsBox h3 {
    height: 20px;
    float: left;
    width: auto;
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
}

/* 统计卡片网格布局 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-top: 30px;
    clear: both;
}

.spider-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 30px;
    clear: both;
}

/* 访问统计卡片样式 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 12px 8px;
    text-align: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    font-size: 20px;
    margin-bottom: 6px;
    animation: pulse 2s infinite;
}

.stat-label {
    font-size: 11px;
    opacity: 0.9;
    margin-bottom: 4px;
    font-weight: 500;
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 蜘蛛统计卡片样式 */
.spider-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 8px;
    padding: 10px 6px;
    text-align: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 70px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.spider-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.4s;
}

.spider-card:hover::before {
    left: 100%;
}

.spider-card:hover {
    transform: translateY(-3px) scale(1.08);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.spider-icon {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.spider-label {
    font-size: 10px;
    opacity: 0.9;
    margin-bottom: 3px;
    font-weight: 500;
}

.spider-value {
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 工具提示样式 */
.stat-card[data-tooltip]:hover::after,
.spider-card[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateX(-50%) translateY(5px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-2px); }
}

/* 数字更新动画 */
.stats-number {
    transition: all 0.3s ease;
}

.stats-number.updated {
    animation: numberUpdate 0.6s ease;
}

@keyframes numberUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #ffd700; }
    100% { transform: scale(1); }
}

/* ==================== 音乐播放器样式 ==================== */
#musicbox {
    border: 1px solid #dae7ed;
    padding: 8px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#musicbox h3 {
    height: 20px;
    float: left;
    width: auto;
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
}

#music-player-container {
    clear: both;
    margin-top: 10px;
}

.music-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.control-btn.play-pause {
    width: 50px;
    height: 50px;
    font-size: 20px;
    background: rgba(255, 255, 255, 0.3);
}

.volume-control {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.volume-slider {
    width: 80px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.volume-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.music-info {
    margin-bottom: 15px;
}

.music-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.music-progress {
    margin-bottom: 5px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #e91e63, #f06292);
    width: 0%;
    transition: width 0.1s ease;
}

.time-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

.music-list {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: bold;
    color: #333;
}

.refresh-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.music-items {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 200px;
    overflow-y: auto;
}

.music-items li {
    padding: 8px 10px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.music-items li:hover {
    background: #f8f9fa;
}

.music-items li.active {
    background: linear-gradient(135deg, #e91e63, #f06292);
    color: white;
}

.music-items li.loading-item {
    text-align: center;
    color: #666;
    font-style: italic;
}

.music-item-title {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 10px;
}

.music-item-play {
    color: #e91e63;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.music-items li:hover .music-item-play {
    opacity: 1;
}

.music-items li.active .music-item-play {
    color: white;
    opacity: 1;
}






/* NEW图标样式 - 使用!important确保优先级 */
.new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    margin-left: 5px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    box-shadow: 0 1px 3px rgba(255, 71, 87, 0.3) !important;
    animation: newPulse 2s infinite !important;
    position: relative !important;
    z-index: 999 !important;
    white-space: nowrap !important;
    line-height: 1 !important;
    border: none !important;
    text-decoration: none !important;
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
}

/* 确保NEW图标在链接中正常显示 */
a .new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
}

/* 悬停效果 */
.new-icon:hover {
    background: linear-gradient(45deg, #ff3742, #ff5865) !important;
    transform: scale(1.1) !important;
}

@keyframes newPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}



/* 确保在不同容器中都能正常显示 */
.newlist .new-icon,
.quicklist .new-icon,
.newslist .new-icon,
li .new-icon,
a .new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    margin-left: 5px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    animation: newPulse 2s infinite !important;
}

/* VIP卡片浏览量徽章样式 */
.view-count-badge {
    position: absolute !important;
    top: 2px !important;
    right: 2px !important;
    background: linear-gradient(45deg, #ff6b35, 
#ff8c42) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold !important;
    font-family: Arial, sans-serif !important;
    padding: 2px 6px !important;
    border-radius: 10px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    z-index: 10 !important;
    white-space: nowrap !important;
    line-height: 1.2 !important;
    text-align: center !important;
    min-width: 30px !important;
    transition: all 0.3s ease !important;
}

/* 浏览量徽章悬停效果 */
.view-count-badge:hover {
    background: linear-gradient(45deg, #ff5722, #ff7043) !important;
    transform: scale(1.1) !important;
    box-shadow: 0 3px 6px rgba(0,0,0,0.3) !important;
}

/* VIP卡片容器相对定位 */
.inlist1 li {
    position: relative !important;
    padding-top: 15px !important;
    min-height: 80px !important;
}

/* VIP卡片图片调整 */
.inlist1 li img {
    margin-top: 5px !important;
}

/* ==================== 选项卡切换样式 ==================== */
.tab-header {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    border: 1px solid #e9ecef;
    border-bottom: none;
}

.tab-item {
    flex: 1;
    margin: 0;
    padding: 12px 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    position: relative;
}

.tab-item:last-child {
    border-right: none;
}

.tab-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.tab-item.active {
    background: #fff;
    border-bottom: 2px solid #007bff;
    transform: translateY(0);
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: #fff;
}

.tab-content {
    background: #fff;
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 8px 8px;
    min-height: 200px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 不同状态的徽章样式 */
.pending-badge {
    background: #ff6600 !important;
    color: white !important;
}

.blacklist-badge {
    background: #333 !important;
    color: white !important;
}

.rejected-badge {
    background: #f39c12 !important;
    color: white !important;
}

/* ==================== 推荐列表样式 ==================== */
.bestlist-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 2px;
    margin: 0;
    padding: 8px;
    list-style: none;
}

.recommend-item {
    position: relative;
}

.recommend-link {
    display: block;
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.recommend-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    transition: left 0.5s;
}

.recommend-link:hover::before {
    left: 100%;
}

.recommend-link:hover {
    border-color: #ffd700;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
}

.recommend-icon {
    position: relative;
    margin-bottom: 8px;
}

.recommend-icon img {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recommend-badge {
    position: absolute;
    top: -8px;
    right: -12px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    font-size: 8px;
    padding: 1px 4px;
    border-radius: 6px;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    animation: bounce 2s infinite;
    z-index: 10;
}

.recommend-name {
    font-size: 11px;
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-width: 100%;
}

/* 热门标签响应式优化 - 平板设备 */
@media (max-width: 1024px) and (min-width: 768px) {
    .hot-tags-container {
        padding: 7px;
        gap: 5px;
    }

    .hot-tag {
        padding: 3px 7px;
        font-size: 11px;
        border-radius: 13px;
    }

    .hot-tag .tag-count {
        font-size: 9px;
        padding: 1px 4px;
        margin-left: 4px;
    }
}

/* 热门标签响应式优化 - 小屏平板 */
@media (max-width: 768px) and (min-width: 481px) {
    .hot-tags-container {
        padding: 6px;
        gap: 4px;
        justify-content: flex-start;
    }

    .hot-tag {
        padding: 3px 6px;
        font-size: 10px;
        border-radius: 12px;
        flex: 0 1 auto;
        max-width: calc(50% - 2px);
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    .hot-tag .tag-count {
        font-size: 8px;
        padding: 1px 3px;
        margin-left: 3px;
    }
}

/* 热门标签响应式优化 - 超小屏设备 */
@media (max-width: 480px) {
    .hot-tags-container {
        padding: 4px;
        gap: 2px;
        justify-content: flex-start;
        max-height: 120px; /* 减少初始高度 */
    }

    .hot-tag {
        padding: 2px 4px;
        font-size: 8px;
        border-radius: 8px;
        flex: 0 1 auto;
        max-width: calc(50% - 1px); /* 每行显示2个 */
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        min-width: 30px;
    }

    .hot-tag.short-tag {
        max-width: calc(25% - 1px); /* 短标签每行显示4个 */
        font-size: 9px;
    }

    .hot-tag.long-tag {
        max-width: calc(100% - 2px); /* 长标签独占一行 */
        font-size: 7px;
    }

    .hot-tag .tag-count {
        font-size: 6px;
        padding: 1px 2px;
        margin-left: 1px;
        border-radius: 4px;
        min-width: 10px;
        text-align: center;
    }

    /* 超小屏设备上的悬停效果进一步减弱 */
    .hot-tag:hover {
        transform: scale(1.01);
        box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    }

    /* 超小屏展开按钮优化 */
    .tags-toggle-btn {
        font-size: 10px;
        padding: 1px 3px;
        margin-top: 0;
    }
}

/* 响应式调整 - 767px以下设备 */
@media (max-width: 767px) {
    /* NEW图标样式 */
    .new-icon {
        font-size: 9px !important;
        padding: 1px 3px !important;
        margin-left: 3px !important;
    }

    /* 浏览量徽章样式 */
    .view-count-badge {
        font-size: 8px !important;
        padding: 1px 4px !important;
        border-radius: 8px !important;
        min-width: 25px !important;
        top: 1px !important;
        right: 1px !important;
    }

    .inlist1 li {
        padding-top: 12px !important;
        min-height: 70px !important;
    }

    .inlist1 li img {
        margin-top: 3px !important;
    }

    /* 推荐列表移动端优化 */
    .bestlist-enhanced {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 6px;
    }

    .recommend-link {
        padding: 8px 6px;
        min-height: 50px;
    }

    .recommend-name {
        font-size: 10px;
    }

    .recommend-icon img {
        width: 24px;
        height: 24px;
    }

    /* 热门标签移动端优化 */
    .hot-tags-container {
        padding: 6px;
        gap: 4px;
        justify-content: center;
    }

    .hot-tag {
        padding: 3px 6px;
        font-size: 11px;
        border-radius: 12px;
        min-width: auto;
        flex-shrink: 1;
    }

    .hot-tag .tag-count {
        font-size: 9px;
        padding: 1px 3px;
        margin-left: 3px;
        border-radius: 8px;
    }

    /* 热门标签悬停效果在移动端减弱 */
    .hot-tag:hover {
        transform: translateY(-1px) scale(1.02);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    /* 展开/收起按钮移动端优化 */
    .tags-toggle-btn {
        font-size: 11px;
        padding: 1px 4px;
        margin-top: -1px;
    }

    .tags-toggle-btn:hover {
        transform: scale(1.05);
    }

    /* 省份标签移动端优化 */
    .province-tags-list .province-tag {
        font-size: 12px;
        padding: 4px 8px;
    }

    /* 统计样式移动端优化 */
    #todayStatsBox, #spiderStatsBox {
        margin: 1%;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        margin-top: 25px;
    }

    .spider-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
        margin-top: 25px;
    }

    .stat-card {
        padding: 10px 6px;
        min-height: 70px;
    }

    .spider-card {
        padding: 8px 4px;
        min-height: 60px;
    }

    .stat-icon {
        font-size: 18px;
    }

    .spider-icon {
        font-size: 16px;
    }

    .stat-value {
        font-size: 14px;
    }

    .spider-value {
        font-size: 12px;
    }

    .stat-label, .spider-label {
        font-size: 10px;
    }

    /* 音乐播放器移动端优化 */
    .music-controls {
        gap: 8px;
        padding: 8px;
    }

    .control-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .control-btn.play-pause {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    .music-title {
        font-size: 13px;
    }

    .music-items {
        max-height: 150px;
    }

    .music-items li {
        padding: 6px 8px;
        font-size: 11px;
    }
}

/* ==================== 首页专用样式类 ==================== */

/* VIP导航链接样式 */
.navbar li a.vip-link {
    color: #ffd700 !important;
}

/* 数据统计区域样式 */
.count.stats-display {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
    text-align: center;
    font-size: 14px;
}

.count.stats-display b.stat-green {
    color: #008800;
}

.count.stats-display b.stat-pink {
    color: #E94E77;
}

.count.stats-display b.stat-success {
    color: #28a745;
}

.count.stats-display b.stat-orange {
    color: #ff6600;
}

.count.stats-display b.stat-warning {
    color: #f39c12;
}

.count.stats-display b.stat-danger {
    color: #dc3545;
}

.count.stats-display b.stat-muted {
    color: #6c757d;
}

/* 轮播公告样式 */
#lunbo {
    list-style-type: none;
    margin-top: 0px;
    font-size: 12px;
}

.site-notice b.stat-highlight {
    font: bold 16px Arial;
}

.site-notice b.stat-green {
    color: #008800;
    font: bold 16px Arial;
}

.site-notice b.stat-pink {
    color: #E94E77;
    font: bold 16px Arial;
}

.site-notice b.stat-success {
    color: #28a745;
    font: bold 16px Arial;
}

.site-notice b.stat-red {
    color: #ff0000;
}

.site-notice span.notice-red {
    color: #ff0000;
}

/* 打赏按钮样式 */
.donate-button.main-donate {
    float: right;
    background-color: #FF6600;
    color: #fff;
    border: none;
    padding: 6px 14px;
    font-size: 14px;
    font-weight: bold;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* VIP卡片链接样式 */
.inlist1 li {
    position: relative;
}

.inlist1 li a.vip-site-link {
    text-decoration: none;
    color: #007bff;
    font-weight: bold;
    transition: color 0.3s;
}

/* 打赏弹窗样式 */
#donate-popup.main-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.donate-popup-content.main-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
}

.donate-popup-content .close.main-close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.donate-popup-content h3.main-title {
    color: #4A90E2;
    font-family: 'Arial', sans-serif;
    font-size: 22px;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    margin-top: 0;
}

.donate-popup-content .service-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.donate-popup-content .service-info p {
    margin: 0;
    line-height: 1.6;
    text-align: center;
}

.donate-popup-content .service-info strong.price-green {
    color: #28a745;
}

.donate-popup-content .service-info strong.price-pink {
    color: #E94E77;
}

.donate-popup-content .service-info strong.price-orange {
    color: #ff6600;
}

.donate-popup-content .note-text {
    text-align: center;
    margin: 15px 0;
    color: #666;
}

.donate-popup-content .note-text strong.highlight-orange {
    color: #F39C12;
}

.donate-qr-codes.main-qr {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
}

.donate-qr-codes .qr-item {
    text-align: center;
}

.donate-qr-codes .qr-image {
    width: 150px;
    height: 150px;
}

.donate-popup-content .service-desc {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-top: 20px;
}

.donate-popup-content .service-desc h4 {
    margin-top: 0;
    color: #333;
}

.donate-popup-content .service-desc ul {
    margin: 0;
    padding-left: 20px;
    line-height: 1.6;
}

/* 图标颜色样式 */
.icon-green {
    color: #28a745;
    margin-right: 5px;
}

.icon-blue {
    color: #007bff;
    margin-right: 5px;
}

.icon-purple {
    color: #6f42c1;
    margin-right: 5px;
}

.icon-info {
    color: #17a2b8;
    margin-right: 5px;
}

.icon-warning {
    color: #fd7e14;
    margin-right: 5px;
}

.icon-success {
    color: #28a745;
    margin-right: 5px;
}

.icon-danger {
    color: #dc3545;
    margin-right: 5px;
}

.icon-pink {
    color: #e91e63;
    margin-right: 5px;
}

.icon-gold {
    color: #ffd700;
    margin-right: 5px;
}

/* 隐藏元素样式 */
.hidden {
    display: none;
}

.archive-loading,
.archive-error {
    display: none;
}

.archive-error.show {
    display: block;
    text-align: center;
    padding: 10px;
    color: #d9534f;
}

/* 选项卡标题样式 */
.tab-item span.tab-recommend {
    color: #07c;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(255, 215, 0, 0.3);
    font-size: 14px;
}

.tab-item span.tab-pending {
    color: #ff6600;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(255, 102, 0, 0.3);
    font-size: 14px;
}

.tab-item span.tab-blacklist {
    color: #333;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(51, 51, 51, 0.3);
    font-size: 14px;
}

.tab-item span.tab-rejected {
    color: #f39c12;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(243, 156, 18, 0.3);
    font-size: 14px;
}

.tab-item .icon-crown {
    color: #ffd700;
    margin-right: 5px;
}

.tab-item .icon-clock {
    color: #ff6600;
    margin-right: 5px;
}

.tab-item .icon-ban {
    color: #333;
    margin-right: 5px;
}

.tab-item .icon-times {
    color: #f39c12;
    margin-right: 5px;
}

/* 音乐播放器标题样式 */
.music-title-span {
    color: #e91e63;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(233, 30, 99, 0.3);
    font-size: 14px;
}

/* 图片滤镜样式 */
.img-filter-pending {
    filter: hue-rotate(30deg) saturate(1.2);
}

.img-filter-blacklist {
    filter: grayscale(100%) brightness(0.5);
}

.img-filter-rejected {
    filter: hue-rotate(30deg) saturate(1.2);
}

/* DR徽章样式 */
.dr-badge {
    vertical-align: text-top;
    margin-top: -1px;
}

/* 蜘蛛统计图标颜色 */
.spider-google {
    color: #4285f4;
}

.spider-baidu {
    color: #2932e1;
}

.spider-bing {
    color: #008373;
}

.spider-sogou {
    color: #fb6c00;
}

.spider-360 {
    color: #01c101;
}

.spider-bytedance {
    color: #ff3040;
}

/* 音乐播放器列表样式 */
.music-item {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.3s;
    position: relative;
}

.music-item:hover {
    background-color: #f5f5f5;
}

.music-item.active {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.music-item .track-info {
    float: left;
    width: calc(100% - 30px);
}

.music-item .track-title {
    font-size: 14px;
    color: #333;
    font-weight: normal;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.music-item .track-controls {
    float: right;
    width: 20px;
    text-align: center;
    color: #666;
    font-size: 12px;
}

.music-item.active .track-controls {
    color: #2196f3;
}

.loading-item, .error-item {
    padding: 15px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.error-item {
    color: #d9534f;
}
